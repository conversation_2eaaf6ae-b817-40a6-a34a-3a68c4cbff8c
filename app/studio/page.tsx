"use client";

import { useEffect, useState } from 'react';
import ClientLayout from '../components/layout/ClientLayout';
import TodaySchedule from '../components/studio/TodaySchedule';
import FinancialSummary from '../components/studio/FinancialSummary';
import NotificationsCenter from '../components/studio/NotificationsCenter';
import QuickActions from '../components/studio/QuickActions';
import { TutorialBanner, TutorialProvider } from '../components/tutorial/TutorialOverlay';

export default function StudioPage() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if we're on mobile
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Check initially
    checkIfMobile();

    // Add resize listener
    window.addEventListener('resize', checkIfMobile);

    // Set up data refresh interval
    const refreshInterval = setInterval(() => {
      // Add refresh logic here
      console.log('Refreshing data...');
    }, 5 * 60 * 1000); // 5 minutes

    // Clean up
    return () => {
      clearInterval(refreshInterval);
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  return (
    <>
      <TutorialProvider>
        <ClientLayout>
          {/* Contenedor con posición fija que se ajusta exactamente entre la barra superior y la navegación inferior */}
          <div className="fixed inset-x-0 top-16 bottom-16 lg:bottom-0 lg:left-64 overflow-hidden">
            <div className="h-full w-full px-2 sm:px-4 md:pr-[450px] lg:pr-96 xl:pr-[350px] 2xl:px-4 2xl:max-w-[calc(100%-350px)] 2xl:mx-0 overflow-y-auto">
              <div className={`space-y-6 p-6 studio-page-container ${isMobile ? 'studio-mobile-styles' : 'studio-desktop-styles'}`}>
                {/* Banner de primeros pasos */}
                <TutorialBanner />

                <h1 className="text-2xl font-bold text-gray-900 mb-4">Mi Estudio</h1>

                {/* Primera fila: Agenda de Hoy */}
                <div className="w-full studio-card">
                  <TodaySchedule />
                </div>

                {/* Segunda fila: Resumen Financiero */}
                <div className="w-full studio-card financial-summary-card">
                  <FinancialSummary />
                </div>

                {/* Tercera fila: Notificaciones y Acciones Rápidas */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="studio-card">
                    <NotificationsCenter />
                  </div>
                  <div className="studio-card">
                    <QuickActions />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ClientLayout>
      </TutorialProvider>
    </>
  );
}