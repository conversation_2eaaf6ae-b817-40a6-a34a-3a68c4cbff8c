import { MetadataRoute } from 'next';

export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://tatu.ink';

  return {
    rules: [
      {
        userAgent: '*',
        allow: [
          '/',
          '/politicas-de-privacidad',
          '/terminos-de-servicio',
          '/politica-de-reembolso'
        ],
        disallow: [
          '/api/',
          '/admin/',
          '/dashboard',
          '/clients',
          '/appointments',
          '/calendar',
          '/studio',
          '/finances',
          '/messages',
          '/settings',
          '/site',
          '/insights',
          '/files',
          '/onboarding',
          '/automations',
          '/analytics',
          '/links',
          '/customization',
          '/portfolio',
          '/notifications',
          '/reviews',
          '/profile',
          '/exito',
          '/fallido',
          '/*/', // Excluir todos los perfiles de usuario (rutas que terminan en /)
        ],
      }
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl,
  };
}
