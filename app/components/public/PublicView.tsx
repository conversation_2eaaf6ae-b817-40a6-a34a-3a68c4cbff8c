'use client'

import React from 'react'
import { Instagram, Facebook, X, Info } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { db } from '@/lib/firebase/config'
import { doc, getDoc, updateDoc, increment } from 'firebase/firestore'
import PublicCalendarPreview from './PublicCalendarPreview'
import PublicLinksPreview from './PublicLinksPreview'
import PortfolioPreview from '../portfolio/PortfolioPreview'
import { PortfolioProvider } from '../portfolio/PortfolioContext'
import PublicReviewsPreview from './PublicReviewsPreview'
import PublicArtistInfoPopup from './PublicArtistInfoPopup'

interface PublicViewProps {
  artistId: string
}

interface ArtistProfile {
  profile: {
    professional?: {
      specialties?: string[]
      languages?: string[]
    }
    contact?: {
      studio?: {
        fullAddress?: string
        coordinates?: {
          lat: number
          lng: number
        }
      }
    }
  }
  customization: {
    brand: {
      name: string
      slogan: string
      facebook?: string
      instagram?: string
      tiktok?: string
    }
    visual: {
      coverImage: string
      logo: string
      primaryColor: string
    }
  }
}

interface PortfolioItem {
  id: string
  title: string
  imageUrl: string
  category: string
  featured: boolean
}

export default function PublicView({ artistId }: PublicViewProps) {
  const [profile, setProfile] = React.useState<ArtistProfile | null>(null)
  const [loading, setLoading] = React.useState(true)
  const [error, setError] = React.useState<string | null>(null)
  const [selectedWork, setSelectedWork] = React.useState<PortfolioItem | null>(null)
  const [showArtistInfo, setShowArtistInfo] = React.useState(false); 
  const [debugInfo, setDebugInfo] = React.useState({
    component: 'PublicView',
    artistId,
    loadStarted: new Date().toISOString()
  })

  // Cargar el perfil del artista
  React.useEffect(() => {
    fetchProfile()
  }, [artistId])

  // Memoize the background style
  const coverStyle = React.useMemo(() => ({
    backgroundColor: profile?.customization.visual?.primaryColor || '#4F46E5',
    backgroundImage: profile?.customization.visual?.coverImage ? `url(${profile.customization.visual.coverImage})` : undefined,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
  }), [profile?.customization.visual?.primaryColor, profile?.customization.visual?.coverImage])

  // Memoize social media links to prevent unnecessary re-renders
  const socialLinks = React.useMemo(() => ({
    instagram: profile?.customization.brand?.instagram,
    facebook: profile?.customization.brand?.facebook,
    tiktok: profile?.customization.brand?.tiktok
  }), [profile?.customization.brand?.instagram, profile?.customization.brand?.facebook, profile?.customization.brand?.tiktok])

  const fetchProfile = async () => {
    if (!artistId) return;
    
    try {
      console.log(`📊 [PublicView] Cargando perfil para artistId: ${artistId}`);
      setLoading(true);
      
      // Obtener datos del perfil desde Firestore
      const profileDoc = await getDoc(doc(db, 'users', artistId))
      
      if (profileDoc.exists()) {
        console.log(`✅ [PublicView] Perfil encontrado para artistId: ${artistId}`);
        const profileData = profileDoc.data() as ArtistProfile;
        setProfile(profileData);
        setLoading(false);
      } else {
        console.error(`❌ [PublicView] Perfil no encontrado para artistId: ${artistId}`);
        setError('Perfil no encontrado en la base de datos');
        setLoading(false);
      }
    } catch (error) {
      console.error('❌ [PublicView] Error cargando perfil:', error)
      setError('Error al cargar el perfil');
      setLoading(false);
    }
  }

  const handleWorkSelect = async (work: PortfolioItem) => {
    setSelectedWork(work)
    // Increment view count
    try {
      const workRef = doc(db, 'portfolio', work.id)
      await updateDoc(workRef, {
        views: increment(1)
      })
    } catch (error) {
      console.error('Error incrementing views:', error)
    }
  }

  // Mostrar estado de carga con información de depuración
  if (loading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-4"></div>
        <div className="text-lg font-semibold">Cargando perfil...</div>
        <div className="mt-4 text-sm text-gray-500">ID del artista: {artistId}</div>
        
        {artistId === 'debug_test_user_id' && (
          <div className="mt-6 p-4 bg-gray-100 rounded-lg max-w-md">
            <div className="font-medium">Modo depuración activo</div>
            <pre className="mt-2 text-xs overflow-auto">
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </div>
        )}
      </div>
    );
  }

  // Mostrar error si ocurre
  if (error || !profile) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong className="font-bold">Error:</strong>
          <span className="block sm:inline"> {error || 'Perfil no encontrado'}</span>
        </div>
        
        <div className="mt-2 text-sm text-gray-500">ID del artista: {artistId}</div>
        
        <div className="mt-6 p-4 bg-gray-100 rounded-lg max-w-md w-full">
          <div className="font-medium">Información de depuración</div>
          <pre className="mt-2 text-xs overflow-auto">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>
        
        <Link 
          href="/"
          className="mt-6 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Volver al inicio
        </Link>
      </div>
    );
  }

  // Extraer datos del perfil de forma segura con valores por defecto
  const brand = profile.customization?.brand || { name: 'Nombre no disponible', slogan: 'Slogan no disponible' };
  const visual = profile.customization?.visual || { coverImage: '', logo: '', primaryColor: '#4F46E5' };

  return (
    <div className="relative">
      {/* Información de depuración para usuario de prueba */}
      {artistId === 'debug_test_user_id' && (
        <div className="bg-yellow-100 p-4 text-sm">
          <p className="font-bold">Modo Depuración</p>
          <p>Este es un perfil de prueba para depuración técnica.</p>
        </div>
      )}
      
      {/* Studio Header with Cover and Logo */}
      <div className="relative">
        {/* Cover Image */}
        <div 
          className="w-full h-32 bg-gradient-to-r from-gray-200 to-gray-300 transition-all duration-300"
          style={coverStyle}
        />

        {/* Logo Container - Positioned half over the cover */}
        <div className="absolute left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="w-20 h-20 rounded-full border-4 border-white shadow-lg overflow-hidden bg-white transition-all duration-300">
            {visual.logo ? (
              <Image 
                src={visual.logo} 
                alt="Logo"
                width={80}
                height={80}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                <span className="text-gray-400 text-2xl">Logo</span>
              </div>
            )}
          </div>
        </div>

        {/* Info Pill */}
        <div className="absolute bottom-2 right-2 z-30">
          <button
            onClick={() => setShowArtistInfo(true)}
            className="bg-white/70 backdrop-blur-sm hover:bg-white text-gray-800 rounded-full p-2 shadow transition-colors"
            aria-label="Información profesional"
          >
            <Info className="w-4 h-4" />
          </button>
        </div>

        {/* Studio Info - Positioned below the logo */}
        <div className="text-center mt-12 px-4">
          <h1 className="text-xl font-bold text-gray-900">
            {brand.name}
          </h1>
          <div className="text-sm text-gray-600 mt-1">
            {brand.slogan}
          </div>

          {/* Social Media Links */}
          <div className="flex justify-center gap-6 mt-2">
            {socialLinks.instagram && (
              <Link
                href={socialLinks.instagram}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-600 hover:text-gray-900 transition-colors"
                aria-label="Instagram"
              >
                <Instagram className="w-5 h-5" />
              </Link>
            )}
            {socialLinks.facebook && (
              <Link
                href={socialLinks.facebook}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-600 hover:text-gray-900 transition-colors"
                aria-label="Facebook"
              >
                <Facebook className="w-5 h-5" />
              </Link>
            )}
            {socialLinks.tiktok && (
              <Link
                href={socialLinks.tiktok}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-600 hover:text-gray-900 transition-colors"
                aria-label="TikTok"
              >
                <svg 
                  className="w-5 h-5" 
                  viewBox="0 0 24 24" 
                  fill="currentColor"
                >
                  <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z"/>
                </svg>
              </Link>
            )}
          </div>
        </div>
      </div>

      {/* Content Sections */}
      <div className="p-4 space-y-6">
        {/* Si es perfil de prueba, mostrar aviso */}
        {artistId === 'debug_test_user_id' && (
          <div className="p-4 border border-yellow-400 bg-yellow-50 rounded-lg">
            <p className="font-medium">Las secciones siguientes no se mostrarán en modo de depuración.</p>
            <p className="text-sm mt-1">En un perfil real verías:</p>
            <ul className="list-disc ml-5 text-sm mt-2">
              <li>Calendario de citas</li>
              <li>Enlaces personalizados</li>
              <li>Portfolio de trabajos</li>
              <li>Reseñas de clientes</li>
            </ul>
          </div>
        )}

        {/* Solo mostrar componentes reales si no estamos en modo depuración */}
        {artistId !== 'debug_test_user_id' && (
          <>
            {/* Calendar Preview */}
            <div>
              <PublicCalendarPreview artistId={artistId} />
            </div>

            {/* Links Preview */}
            <div>
              <PublicLinksPreview artistId={artistId} />
            </div>

            {/* Portfolio Preview */}
            <div className="relative">
              <PortfolioProvider userId={artistId}>
                <PortfolioPreview userId={artistId} onWorkSelect={handleWorkSelect} />
              </PortfolioProvider>
            </div>

            {/* Reviews Preview */}
            <div>
              <PublicReviewsPreview artistId={artistId} />
            </div>
          </>
        )}
      </div>

      {/* Work Modal */}
      {selectedWork && (
        <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center p-4">
          <div className="relative w-full max-w-3xl max-h-[90vh] overflow-hidden rounded-lg">
            <Image
              src={selectedWork.imageUrl}
              alt={selectedWork.title}
              width={1200}
              height={800}
              className="w-full h-auto object-contain max-h-[80vh]"
            />
            
            <button
              onClick={() => setSelectedWork(null)}
              className="absolute top-4 right-4 p-2 bg-black/50 hover:bg-black/70 rounded-full text-white z-10"
            >
              <X className="w-5 h-5" />
            </button>
            
            <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent">
              <h2 className="text-lg font-semibold text-white">{selectedWork.title}</h2>
              <p className="text-sm text-gray-200">{selectedWork.category}</p>
            </div>
          </div>
        </div>
      )}

      {/* Popup de información profesional */}
      {/* Modal de Información Profesional */}
      {showArtistInfo && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <PublicArtistInfoPopup onClose={() => setShowArtistInfo(false)} profile={profile} />
          </div>
        </div>
      )}

      {/* Sección promocional de Tatu.Ink */}
      <div className="mt-8 py-6 bg-gray-100 border-t border-gray-200">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-2xl mx-auto">
            <h3 className="text-lg font-medium text-gray-800 mb-2">¿Eres tatuador profesional?</h3>
            <p className="mb-4 text-gray-600">Crea tu propio portafolio digital, gestiona tus citas y conecta con nuevos clientes.</p>
            <a 
              href="https://tatu.ink" 
              target="_blank" 
              rel="noopener noreferrer"
              className="inline-block px-6 py-2 bg-gray-800 text-gray-100 font-medium rounded hover:bg-black transition-colors duration-200"
            >
              Crea tu perfil en Tatu.Ink
            </a>
            <p className="mt-3 text-xs text-gray-500">Plataforma especializada para profesionales del tatuaje</p>
          </div>
        </div>
      </div>
    </div>
  )
}
