'use client';

import React, { useState, useEffect } from "react";
import { Fragment } from "react";
import { Menu, Transition } from "@headlessui/react";
import {
  Bars3Icon,
  BellIcon,
  CalendarIcon,
  ChevronDownIcon,
  MagnifyingGlassIcon,
  UserCircleIcon,
  UserPlusIcon,
} from "@heroicons/react/24/outline";
import { UserCircleIcon as UserCircleSolidIcon } from "@heroicons/react/24/solid";
import {
  ChevronRightIcon,
  CreditCardIcon,
  EllipsisVerticalIcon,
} from "@heroicons/react/20/solid";
import Link from "next/link";
import NotificationsDropdown from './NotificationsDropdown';
import { getFirestore, getDoc, doc } from 'firebase/firestore';
import { getDatabase, ref, get } from 'firebase/database';
import { useAuth } from '@/app/contexts/AuthContext'
import { useProfile } from '@/app/contexts/ProfileContext'
import { app } from '@/lib/firebase/config'
import { db } from '@/lib/firebase/config'
import { useAssistant } from '@/app/contexts/AssistantContext';
import { SunIcon, MoonIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { Robot, Power, X, Sparkle, Brain, ListChecks, PlusCircle, CheckSquare, Info } from '@phosphor-icons/react';
import { useTheme } from 'next-themes';
import ProfileImage from './ProfileImage';
import { Dialog } from '@headlessui/react';
import { useRef } from 'react';
import { signOut } from 'firebase/auth';
import AssistantConfig from '../messages/AssistantConfig';
import { useRouter } from "next/navigation";
import clsx from 'clsx';

interface TopbarProps {
  onOpenSidebar: () => void;
}

function AssistantConfigModal({ 
  isOpen, 
  onClose 
}: { 
  isOpen: boolean; 
  onClose: () => void; 
}) {
  const { config, updateConfig } = useAssistant();

  const handleBasePromptChange = (prompt: string) => {
    if (!config) return;
    console.log('Actualizando prompt base del asistente:', prompt);
    updateConfig({
      ...config,
      basePrompt: prompt
    });
  };

  const handleRequiredFieldsChange = (fields: string[]) => {
    if (!config) return;
    console.log('Actualizando campos requeridos:', fields);
    updateConfig({
      ...config,
      requiredFields: fields
    });
  };

  if (!config) return null;

  return (
    <Dialog as="div" className="relative z-50" open={isOpen} onClose={onClose}>
      <div className="fixed inset-0 bg-black/40 backdrop-blur-sm" aria-hidden="true" />
      
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="w-full max-w-3xl rounded-2xl overflow-hidden">
          <AssistantConfig
            isOpen={isOpen}
            onClose={onClose}
            basePrompt={config.basePrompt || ''}
            onBasePromptChange={handleBasePromptChange}
            requiredFields={config.requiredFields || ['Diseño a tatuar', 'Tamaño del tatuaje', 'Lugar del cuerpo', 'Estilo de tatuaje']}
            onRequiredFieldsChange={handleRequiredFieldsChange}
          />
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}

export default function Topbar({ onOpenSidebar }: { onOpenSidebar: () => void }) {
  const { profile } = useProfile();
  const { user, signOut: signOutAuth, subscriptionStatus } = useAuth();
  const { theme, setTheme } = useTheme();
  const { isEnabled, toggleAssistant } = useAssistant();
  const [showConfig, setShowConfig] = useState(false);
  const [username, setUsername] = useState('');
  const router = useRouter();
  
  // Verificar si el usuario tiene suscripción Pro
  const isPro = subscriptionStatus === 'active';

  // Fetch username
  useEffect(() => {
    const fetchUsername = async () => {
      if (!user) return;
      
      try {
        // Método 1: Buscar en Firestore
        const firestoreDb = getFirestore(app);
        try {
          const userDoc = await getDoc(doc(firestoreDb, 'users', user.uid));
          if (userDoc.exists() && userDoc.data().username) {
            const firestoreUsername = userDoc.data().username;
            setUsername(firestoreUsername);
            return;
          }
        } catch (firestoreError) {
          console.error('Error buscando en Firestore:', firestoreError);
        }
        
        // Método 2: Buscar en RTDB
        const rtdb = getDatabase(app);
        const usernamesRef = ref(rtdb, 'usernames');
        const snapshot = await get(usernamesRef);
        
        if (snapshot.exists()) {
          snapshot.forEach((childSnapshot) => {
            if (childSnapshot.val() === user.uid) {
              setUsername(childSnapshot.key);
            }
          });
        }
      } catch (error) {
        console.error('Error obteniendo username:', error);
      }
    };
    
    fetchUsername();
  }, [user]);

  return (
    <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-900 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
      {/* Sidebar button */}
      <button
        type="button"
        className="-m-2.5 p-2.5 text-gray-700 dark:text-gray-200 lg:hidden"
        onClick={onOpenSidebar}
      >
        <span className="sr-only">Abrir sidebar</span>
        <Bars3Icon className="h-6 w-6" aria-hidden="true" />
      </button>

      {/* Separator */}
      <div className="h-6 w-px bg-gray-200 dark:bg-gray-800 lg:hidden" aria-hidden="true" />

      {/* Website URL */}
      {username && (
        <div className="flex items-center space-x-4">
          <a
            href={`${window.location.protocol}//tatu.ink/${username}`}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center px-3 py-1.5 rounded-lg bg-black text-white hover:bg-gray-900 transition-colors text-sm"
          >
            <span className="hidden md:inline mr-1 font-bold">Ver mi sitio:</span>
            <span>tatu.ink/{username}</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 ml-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
              />
            </svg>
          </a>
        </div>
      )}

      <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6 justify-end">
        <div className="flex items-center gap-x-4 lg:gap-x-6">
          {/* Notifications dropdown */}
          <NotificationsDropdown />
          
          {/* Assistant toggle - Solo visible para usuarios Pro */}
          {isPro && (
            <div className="flex items-center gap-2">
              <button
                onClick={toggleAssistant}
                className={
                  isEnabled 
                    ? "flex items-center gap-1 px-3 py-2 rounded-l-full shadow-sm bg-gradient-to-r from-purple-600 to-indigo-600 text-white hover:from-purple-700 hover:to-indigo-700 transition-all duration-300" 
                    : "flex items-center gap-1 px-3 py-2 rounded-l-full shadow-sm bg-gray-200 text-gray-600 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 transition-all duration-300"
                }
                title={isEnabled ? "Desactivar asistente" : "Activar asistente"}
              >
                <Power className="h-4 w-4" />
                <span className="text-sm font-medium hidden sm:inline">
                  {isEnabled ? "Activo" : "Inactivo"}
                </span>
              </button>
              <button
                onClick={() => setShowConfig(true)}
                className={clsx(
                  "flex items-center gap-1 px-3 py-2 rounded-r-full shadow-sm transition-all duration-300",
                  isEnabled 
                    ? "bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:from-indigo-700 hover:to-purple-700" 
                    : "bg-gray-200 text-gray-600 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                )}
                title="Configurar asistente"
              >
                <SparklesIcon className="h-4 w-4" />
                <span className="text-sm font-medium hidden sm:inline">Configurar</span>
              </button>
            </div>
          )}

          {/* Profile dropdown */}
          <div className="flex items-center gap-4">
            <Menu as="div" className="relative">
              <Menu.Button className="-m-1.5 flex items-center p-1.5">
                <span className="sr-only">Abrir menú de usuario</span>
                <ProfileImage />
              </Menu.Button>
              <Transition
                as={Fragment}
                enter="transition ease-out duration-100"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
              >
                <Menu.Items className="absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white dark:bg-gray-800 py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        onClick={() => signOutAuth()}
                        className={`${
                          active ? 'bg-gray-50 dark:bg-gray-700' : ''
                        } block px-3 py-1 text-sm leading-6 text-gray-900 dark:text-gray-200 w-full text-left`}
                      >
                        Cerrar sesión
                      </button>
                    )}
                  </Menu.Item>
                </Menu.Items>
              </Transition>
            </Menu>
          </div>
        </div>
      </div>

      <AssistantConfigModal isOpen={showConfig} onClose={() => setShowConfig(false)} />
    </div>
  );
}
