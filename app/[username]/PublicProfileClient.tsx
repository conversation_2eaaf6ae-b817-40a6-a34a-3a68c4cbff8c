'use client';

import { useEffect, useState } from 'react';
import { getDatabase } from 'firebase/database';
import { app } from '@/lib/firebase/config';
import UserNotFound from '@/app/components/public/UserNotFound';
import { getUserIdFromUsername } from '@/app/utils/username';
import PublicView from '@/app/components/public/PublicView';
import { trackPageView } from '@/app/utils/analytics';

interface PublicProfileClientProps {
  username: string;
}

export default function PublicProfileClient({ username }: PublicProfileClientProps) {
  // Estados
  const [userId, setUserId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [notFoundState, setNotFoundState] = useState(false);

  useEffect(() => {
    if (!username) return;

    // Actualizar document.title
    document.title = `Perfil de ${username} | Tatu.Ink`;
    console.log(`🔍 [PublicProfileClient] Buscando perfil para username: ${username}`);

    // Función para obtener el ID del usuario
    async function fetchUserId() {
      try {
        const db = getDatabase(app);
        console.log(`🔍 [PublicProfileClient] Conectado a Firebase Realtime Database`);

        // Obtener ID del usuario
        const id = await getUserIdFromUsername(db, username);

        if (!id) {
          console.log(`⚠️ [PublicProfileClient] No se encontró ID para username: ${username}`);
          setNotFoundState(true);
          setLoading(false);
          return;
        }

        console.log(`✅ [PublicProfileClient] ID encontrado para username ${username}: ${id}`);

        // Registrar la visita al perfil
        try {
          console.log(`📊 [PublicProfileClient] Registrando visita para userId: ${id}, username: ${username}`);
          await trackPageView(id, username, 'profile');
          console.log(`✅ [PublicProfileClient] Visita registrada correctamente`);
        } catch (trackError) {
          console.error(`❌ [PublicProfileClient] Error registrando visita:`, trackError);
          // No interrumpimos el flujo si falla el tracking
        }

        setUserId(id);
        setLoading(false);
      } catch (error) {
        console.error(`❌ [PublicProfileClient] Error buscando usuario:`, error);
        setNotFoundState(true);
        setLoading(false);
      }
    }

    fetchUserId();
  }, [username]);

  // Renderizar el componente PublicView con el ID del usuario
  return (
    <div className="relative">
      {loading ? (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-gray-900"></div>
          <div className="ml-4 text-gray-600">Cargando perfil de {username}...</div>
        </div>
      ) : notFoundState ? (
        <UserNotFound username={username} />
      ) : userId ? (
        <PublicView artistId={userId} />
      ) : (
        <div className="p-4 bg-red-100 text-red-800">
          Estado inesperado: no hay userId pero tampoco notFoundState
        </div>
      )}
    </div>
  );
}
