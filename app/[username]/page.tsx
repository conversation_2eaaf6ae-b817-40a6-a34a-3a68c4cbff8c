import { Metadata } from 'next';
import PublicProfileClient from './PublicProfileClient';

// Forzar renderizado dinámico
export const dynamic = 'force-dynamic';
export const fetchCache = 'default-no-store';
export const revalidate = 0;

// Generar metadata para SEO - NOINDEX para perfiles de usuario
export async function generateMetadata({ params }: { params: { username: string } }): Promise<Metadata> {
  return {
    title: `Perfil de ${params.username} | Tatu.Ink`,
    description: `Perfil profesional de ${params.username} en Tatu.Ink`,
    robots: {
      index: false, // NO indexar perfiles de usuario
      follow: false, // NO seguir enlaces en perfiles de usuario
      nocache: true,
      noarchive: true,
      nosnippet: true,
    },
    // NO incluir canonical URL para páginas no indexables
  };
}

interface PageProps {
  params: {
    username: string;
  }
}

export default function PublicProfilePage({ params }: PageProps) {
  // Pasar el username al componente cliente
  return <PublicProfileClient username={params.username} />;
}
