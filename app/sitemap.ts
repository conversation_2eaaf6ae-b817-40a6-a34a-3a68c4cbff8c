import { MetadataRoute } from 'next';
import { getFirestore } from 'firebase-admin/firestore';
import { getApp, initializeApp } from 'firebase-admin/app';
import { db as adminDb } from '@/lib/firebase/firebase-admin';

// Define tu URL base
const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://tatu.ink';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // Rutas estáticas principales - SOLO páginas públicas que queremos indexar
  const staticRoutes = [
    '',
    '/politicas-de-privacidad',
    '/terminos-de-servicio',
    '/politica-de-reembolso',
  ].map(route => ({
    url: `${baseUrl}${route}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as 'monthly',
    priority: route === '' ? 1 : 0.8,
  }));

  // NO incluir perfiles de usuario en el sitemap
  // Los perfiles de usuario no deben ser indexados por Google

  // NO incluir páginas de éxito/fallo ya que son páginas de resultado
  // NO incluir rutas dinámicas de perfiles de usuario

  console.log('Sitemap generado solo con páginas estáticas principales');

  return staticRoutes;
}
