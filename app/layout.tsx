import { Inter } from 'next/font/google';
import './globals.css';
import Providers from './providers';
import AnalyticsScript from './components/analytics/AnalyticsScript';
import DomainCheck from './components/DomainCheck';
import PageSpecificStyles from './components/PageSpecificStyles';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'Tatu.Ink - Plataforma para Artistas del Tatuaje',
  description: 'La plataforma diseñada específicamente para artistas del tatuaje independientes. Gestiona tu agenda, filtra clientes serios, muestra tu portafolio y aumenta tus ingresos.',
  keywords: 'artista del tatuaje, tatuador independiente, gestión de tatuajes, portafolio de tatuajes, agenda para tatuadores, clientes de tatuajes',
  metadataBase: new URL('https://tatu.ink'),
  alternates: {
    canonical: 'https://tatu.ink',
  },
  icons: {
    icon: '/favicon/favicon.ico',
    apple: '/favicon/apple-touch-icon.png',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
    },
  },
  openGraph: {
    title: 'Tatu.Ink - Plataforma para Artistas del Tatuaje',
    description: 'La plataforma diseñada específicamente para artistas del tatuaje independientes. Gestiona tu agenda, filtra clientes serios, muestra tu portafolio y aumenta tus ingresos.',
    type: 'website',
    locale: 'es_ES',
    url: 'https://tatu.ink',
    siteName: 'Tatu.Ink',
    images: [
      {
        url: '/favicon/apple-touch-icon.png',
        width: 500,
        height: 500,
        alt: 'Tatu.Ink Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Tatu.Ink - Plataforma para Artistas del Tatuaje',
    description: 'La plataforma diseñada específicamente para artistas del tatuaje independientes. Gestiona tu agenda, filtra clientes serios, muestra tu portafolio y aumenta tus ingresos.',
    images: ['/favicon/apple-touch-icon.png'],
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="es" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon/favicon.ico" sizes="any" />
        <link rel="apple-touch-icon" href="/favicon/apple-touch-icon.png" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&family=MedievalSharp&family=Montserrat:wght@300;400;500;600;700&family=New+Rocker&family=Nosifer&family=Permanent+Marker&family=Roboto:wght@300;400;500;700&family=UnifrakturMaguntia&display=swap" rel="stylesheet" />
      </head>
      <body suppressHydrationWarning className={inter.className}>
        <Providers>
          <DomainCheck>
            <PageSpecificStyles />
            {children}
          </DomainCheck>
        </Providers>
        <AnalyticsScript />
      </body>
    </html>
  );
}
